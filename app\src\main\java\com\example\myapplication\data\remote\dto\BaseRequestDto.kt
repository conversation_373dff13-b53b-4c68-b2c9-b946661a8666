package com.example.myapplication.data.remote.dto

import com.google.gson.annotations.SerializedName

/**
 * Classe base para todos os DTOs de requisição da API
 */
data class BaseRequestDto(
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),

    @SerializedName("device_id")
    val deviceId: String? = null,

    @SerializedName("app_version")
    val appVersion: String? = null
)

/**
 * DTO para requisição de login
 */
data class LoginRequestDto(
    @SerializedName("usernameOrEmail")
    val usernameOrEmail: String,

    @SerializedName("password")
    val password: String
)