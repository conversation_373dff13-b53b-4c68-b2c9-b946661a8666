{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-46:/values-el/values-el.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\53ae32e60a4659d4c9f24c895da50bc6\\transformed\\material3-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,417,539,643,746,866,1017,1145,1303,1393,1493,1592,1697,1815,1941,2046,2188,2324,2468,2648,2786,2906,3033,3157,3257,3356,3492,3629,3735,3841,3951,4095,4248,4362,4468,4555,4653,4750,4863,4953,5042,5145,5225,5308,5407,5509,5606,5704,5791,5897,5996,6098,6219,6299,6415", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "174,298,412,534,638,741,861,1012,1140,1298,1388,1488,1587,1692,1810,1936,2041,2183,2319,2463,2643,2781,2901,3028,3152,3252,3351,3487,3624,3730,3836,3946,4090,4243,4357,4463,4550,4648,4745,4858,4948,5037,5140,5220,5303,5402,5504,5601,5699,5786,5892,5991,6093,6214,6294,6410,6517"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1498,1622,1746,1860,1982,2086,2189,2309,2460,2588,2746,2836,2936,3035,3140,3258,3384,3489,3631,3767,3911,4091,4229,4349,4476,4600,4700,4799,4935,5072,5178,5284,5394,5538,5691,5805,5911,5998,6096,6193,6306,6396,6485,6588,6668,6751,6850,6952,7049,7147,7234,7340,7439,7541,7662,7742,7858", "endColumns": "123,123,113,121,103,102,119,150,127,157,89,99,98,104,117,125,104,141,135,143,179,137,119,126,123,99,98,135,136,105,105,109,143,152,113,105,86,97,96,112,89,88,102,79,82,98,101,96,97,86,105,98,101,120,79,115,106", "endOffsets": "1617,1741,1855,1977,2081,2184,2304,2455,2583,2741,2831,2931,3030,3135,3253,3379,3484,3626,3762,3906,4086,4224,4344,4471,4595,4695,4794,4930,5067,5173,5279,5389,5533,5686,5800,5906,5993,6091,6188,6301,6391,6480,6583,6663,6746,6845,6947,7044,7142,7229,7335,7434,7536,7657,7737,7853,7960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\54dc2f522003cf92f07cd74296fe5ae5\\transformed\\ui-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,391,496,588,669,763,852,942,1023,1105,1180,1255,1333,1408,1487,1557", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "199,285,386,491,583,664,758,847,937,1018,1100,1175,1250,1328,1403,1482,1552,1675"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,939,1025,1126,1231,1323,1404,7965,8054,8144,8225,8307,8382,8457,8535,8711,8790,8860", "endColumns": "98,85,100,104,91,80,93,88,89,80,81,74,74,77,74,78,69,122", "endOffsets": "934,1020,1121,1226,1318,1399,1493,8049,8139,8220,8302,8377,8452,8530,8605,8785,8855,8978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed91d6823b14a2d62ff6e32e9475a580\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,406,509,617,723,8610", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "198,301,401,504,612,718,835,8706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ae7943ae170c0ca23ecd0d84ed8b062\\transformed\\foundation-release\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8983,9081", "endColumns": "97,101", "endOffsets": "9076,9178"}}]}]}