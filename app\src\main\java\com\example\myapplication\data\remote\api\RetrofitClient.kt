package com.example.myapplication.data.remote.api

import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * Objeto singleton responsável por criar e configurar a instância do Retrofit
 * para comunicação com a API.
 *
 * Segue o padrão Singleton para garantir que uma única instância do Retrofit
 * seja usada em todo o aplicativo, melhorando o desempenho e a consistência.
 */
object RetrofitClient {
    /**
     * URL base da API.
     * Usando ********, que é o endereço IP especial para localhost ao rodar
     * no emulador Android. Para dispositivos físicos, substitua pelo seu IP real.
     */
    private const val BASE_URL = "http://**************:3000/"

    /**
     * Instância ApiService inicializada de forma preguiçosa (lazy).
     * O delegate 'by lazy' garante que a instância do Retrofit seja criada apenas quando for acessada pela primeira vez,
     * e a mesma instância é reutilizada para chamadas subsequentes.
     */
    val apiService: ApiService by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            .create(ApiService::class.java)
    }
}