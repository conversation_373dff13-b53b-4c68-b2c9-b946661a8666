package com.example.myapplication.data.repository

import com.example.myapplication.data.remote.api.ApiService
import com.example.myapplication.data.remote.dto.LoginRequestDto
import com.example.myapplication.data.remote.dto.ErrorResponseDto
import com.example.myapplication.domain.model.AuthError
import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.Resource
import com.example.myapplication.domain.model.UserLogin
import com.example.myapplication.domain.repository.AuthRepository
import com.google.gson.Gson
import retrofit2.HttpException
import java.io.IOException

/**
 * Implementação do repositório de autenticação
 */
class AuthRepositoryImpl(
    private val apiService: ApiService
) : AuthRepository {

    override suspend fun login(loginRequest: LoginRequest): Resource<LoginResponse> {
        return try {
            val requestDto = LoginRequestDto(
                usernameOrEmail = loginRequest.usernameOrEmail,
                password = loginRequest.password
            )
            
            val response = apiService.login(requestDto)
            
            if (response.isSuccessful) {
                response.body()?.let { loginResponseDto ->
                    val loginResponse = LoginResponse(
                        accessToken = loginResponseDto.accessToken,
                        refreshToken = loginResponseDto.refreshToken,
                        tokenType = loginResponseDto.tokenType,
                        expiresIn = loginResponseDto.expiresIn,
                        user = UserLogin(
                            id = loginResponseDto.user.id,
                            username = loginResponseDto.user.username,
                            email = loginResponseDto.user.email,
                            fullName = loginResponseDto.user.fullName,
                            avatar = loginResponseDto.user.avatar,
                            isActive = loginResponseDto.user.isActive,
                            createdAt = loginResponseDto.user.createdAt,
                            updatedAt = loginResponseDto.user.updatedAt
                        )
                    )
                    Resource.Success<LoginResponse>(loginResponse)
                } ?: Resource.Error<LoginResponse>("Resposta vazia do servidor")
            } else {
                // Tentar parsear o erro da API
                val errorBody = response.errorBody()?.string()
                val authError = try {
                    val errorResponse = Gson().fromJson(errorBody, ErrorResponseDto::class.java)
                    val message = when (errorResponse.message) {
                        is String -> errorResponse.message
                        is List<*> -> (errorResponse.message as List<String>).joinToString(", ")
                        else -> "Erro desconhecido"
                    }
                    AuthError(
                        statusCode = errorResponse.statusCode,
                        message = message,
                        error = errorResponse.error,
                        code = errorResponse.code
                    )
                } catch (e: Exception) {
                    AuthError(
                        statusCode = response.code(),
                        message = "Erro de autenticação",
                        error = "Bad Request"
                    )
                }
                Resource.Error<LoginResponse>(authError.message, null)
            }
        } catch (e: HttpException) {
            Resource.Error<LoginResponse>("Erro de rede: ${e.message()}")
        } catch (e: IOException) {
            Resource.Error<LoginResponse>("Erro de conexão: Verifique sua internet")
        } catch (e: Exception) {
            Resource.Error<LoginResponse>("Erro inesperado: ${e.message}")
        }
    }
}
