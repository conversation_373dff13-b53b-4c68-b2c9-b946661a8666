{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-46:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ae7943ae170c0ca23ecd0d84ed8b062\\transformed\\foundation-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8925,9013", "endColumns": "87,94", "endOffsets": "9008,9103"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed91d6823b14a2d62ff6e32e9475a580\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,306,407,513,614,722,8560", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "198,301,402,508,609,717,845,8656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\54dc2f522003cf92f07cd74296fe5ae5\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,1006,1092,1171,1246,1324,1401,1478,1547", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,1001,1087,1166,1241,1319,1396,1473,1542,1660"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,947,1031,1127,1227,1316,1400,7907,7998,8083,8165,8251,8330,8405,8483,8661,8738,8807", "endColumns": "96,83,95,99,88,83,92,90,84,81,85,78,74,77,76,76,68,117", "endOffsets": "942,1026,1122,1222,1311,1395,1488,7993,8078,8160,8246,8325,8400,8478,8555,8733,8802,8920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\53ae32e60a4659d4c9f24c895da50bc6\\transformed\\material3-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,425,548,647,745,860,1017,1147,1299,1385,1491,1587,1689,1805,1938,2049,2188,2323,2456,2634,2758,2876,2997,3124,3221,3318,3440,3578,3684,3793,3899,4038,4183,4293,4402,4478,4578,4678,4794,4881,4970,5081,5161,5245,5345,5453,5553,5654,5741,5854,5956,6061,6182,6262,6372", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "174,298,420,543,642,740,855,1012,1142,1294,1380,1486,1582,1684,1800,1933,2044,2183,2318,2451,2629,2753,2871,2992,3119,3216,3313,3435,3573,3679,3788,3894,4033,4178,4288,4397,4473,4573,4673,4789,4876,4965,5076,5156,5240,5340,5448,5548,5649,5736,5849,5951,6056,6177,6257,6367,6464"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1493,1617,1741,1863,1986,2085,2183,2298,2455,2585,2737,2823,2929,3025,3127,3243,3376,3487,3626,3761,3894,4072,4196,4314,4435,4562,4659,4756,4878,5016,5122,5231,5337,5476,5621,5731,5840,5916,6016,6116,6232,6319,6408,6519,6599,6683,6783,6891,6991,7092,7179,7292,7394,7499,7620,7700,7810", "endColumns": "123,123,121,122,98,97,114,156,129,151,85,105,95,101,115,132,110,138,134,132,177,123,117,120,126,96,96,121,137,105,108,105,138,144,109,108,75,99,99,115,86,88,110,79,83,99,107,99,100,86,112,101,104,120,79,109,96", "endOffsets": "1612,1736,1858,1981,2080,2178,2293,2450,2580,2732,2818,2924,3020,3122,3238,3371,3482,3621,3756,3889,4067,4191,4309,4430,4557,4654,4751,4873,5011,5117,5226,5332,5471,5616,5726,5835,5911,6011,6111,6227,6314,6403,6514,6594,6678,6778,6886,6986,7087,7174,7287,7389,7494,7615,7695,7805,7902"}}]}]}