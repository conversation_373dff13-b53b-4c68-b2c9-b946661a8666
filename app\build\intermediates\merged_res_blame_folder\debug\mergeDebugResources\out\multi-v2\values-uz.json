{"logs": [{"outputFile": "com.example.myapplication.app-mergeDebugResources-46:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ae7943ae170c0ca23ecd0d84ed8b062\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,100", "endOffsets": "161,262"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8731,8842", "endColumns": "110,100", "endOffsets": "8837,8938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ed91d6823b14a2d62ff6e32e9475a580\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,207,309,410,510,618,722,8362", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "202,304,405,505,613,717,836,8458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\53ae32e60a4659d4c9f24c895da50bc6\\transformed\\material3-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,401,515,612,710,825,958,1067,1209,1293,1397,1491,1589,1703,1824,1933,2058,2181,2311,2479,2604,2725,2849,2970,3065,3163,3280,3406,3510,3620,3727,3850,3978,4091,4195,4279,4375,4469,4599,4687,4773,4874,4954,5038,5138,5242,5338,5437,5525,5633,5733,5836,5975,6055,6171", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "167,282,396,510,607,705,820,953,1062,1204,1288,1392,1486,1584,1698,1819,1928,2053,2176,2306,2474,2599,2720,2844,2965,3060,3158,3275,3401,3505,3615,3722,3845,3973,4086,4190,4274,4370,4464,4594,4682,4768,4869,4949,5033,5133,5237,5333,5432,5520,5628,5728,5831,5970,6050,6166,6269"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1505,1622,1737,1851,1965,2062,2160,2275,2408,2517,2659,2743,2847,2941,3039,3153,3274,3383,3508,3631,3761,3929,4054,4175,4299,4420,4515,4613,4730,4856,4960,5070,5177,5300,5428,5541,5645,5729,5825,5919,6049,6137,6223,6324,6404,6488,6588,6692,6788,6887,6975,7083,7183,7286,7425,7505,7621", "endColumns": "116,114,113,113,96,97,114,132,108,141,83,103,93,97,113,120,108,124,122,129,167,124,120,123,120,94,97,116,125,103,109,106,122,127,112,103,83,95,93,129,87,85,100,79,83,99,103,95,98,87,107,99,102,138,79,115,102", "endOffsets": "1617,1732,1846,1960,2057,2155,2270,2403,2512,2654,2738,2842,2936,3034,3148,3269,3378,3503,3626,3756,3924,4049,4170,4294,4415,4510,4608,4725,4851,4955,5065,5172,5295,5423,5536,5640,5724,5820,5914,6044,6132,6218,6319,6399,6483,6583,6687,6783,6882,6970,7078,7178,7281,7420,7500,7616,7719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\54dc2f522003cf92f07cd74296fe5ae5\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1026,1109,1183,1259,1334,1407,1490,1558", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1021,1104,1178,1254,1329,1402,1485,1553,1670"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "841,940,1025,1129,1236,1332,1415,7724,7817,7900,7981,8064,8138,8214,8289,8463,8546,8614", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,75,74,72,82,67,116", "endOffsets": "935,1020,1124,1231,1327,1410,1500,7812,7895,7976,8059,8133,8209,8284,8357,8541,8609,8726"}}]}]}