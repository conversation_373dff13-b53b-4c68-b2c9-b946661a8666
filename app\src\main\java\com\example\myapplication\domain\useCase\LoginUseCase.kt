package com.example.myapplication.domain.useCase

import com.example.myapplication.domain.model.LoginRequest
import com.example.myapplication.domain.model.LoginResponse
import com.example.myapplication.domain.model.Resource
import com.example.myapplication.domain.repository.AuthRepository

/**
 * Use case para realizar login
 */
class LoginUseCase(
    private val authRepository: AuthRepository
) {
    suspend operator fun invoke(usernameOrEmail: String, password: String): Resource<LoginResponse> {
        // Validações básicas
        if (usernameOrEmail.isBlank()) {
            return Resource.Error<LoginResponse>("Nome de usuário ou email é obrigatório")
        }

        if (password.isBlank()) {
            return Resource.Error<LoginResponse>("Senha é obrigatória")
        }

        if (password.length < 6) {
            return Resource.Error<LoginResponse>("Senha deve ter pelo menos 6 caracteres")
        }
        
        val loginRequest = LoginRequest(
            usernameOrEmail = usernameOrEmail.trim(),
            password = password
        )
        
        return authRepository.login(loginRequest)
    }
}
